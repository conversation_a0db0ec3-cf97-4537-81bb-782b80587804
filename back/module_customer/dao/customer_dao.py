from datetime import datetime, time
from sqlalchemy import and_, delete, desc, func, or_, select, update
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List
from datetime import datetime, time
from module_admin.entity.do.user_do import SysUser
from module_customer.entity.do.customer_do import Customer, CustomerContact, CustomerInternalManager
from module_customer.entity.vo.customer_vo import CustomerPageQueryModel, CustomerQueryModel
from utils.page_util import PageUtil


class CustomerDao:
    """
    客户管理模块数据库操作层
    """

    @classmethod
    async def get_customer_list(cls, db: AsyncSession, query_object: CustomerQueryModel):
        """
        获取客户列表

        :param db: orm对象
        :param query_object: 查询参数对象
        :return: 客户列表
        """
        query = select(Customer).where(Customer.del_flag == '0')

        # 根据查询条件过滤
        if query_object.customer_name:
            query = query.where(Customer.customer_name.like(f'%{query_object.customer_name}%'))
        if query_object.customer_level:
            query = query.where(Customer.customer_level == query_object.customer_level)
        if query_object.customer_type:
            query = query.where(Customer.customer_type == query_object.customer_type)
        if query_object.customer_source:
            query = query.where(Customer.customer_source == query_object.customer_source)
        if query_object.province:
            query = query.where(Customer.province == query_object.province)
        if query_object.city:
            query = query.where(Customer.city == query_object.city)
        if query_object.status:
            query = query.where(Customer.status == query_object.status)
        if hasattr(query_object, 'customer_id') and query_object.customer_id:
            query = query.where(Customer.customer_id == query_object.customer_id)
        if hasattr(query_object, 'parent_id') and query_object.parent_id:
            query = query.where(Customer.parent_id == query_object.parent_id)

        # 时间范围查询
        if query_object.begin_time and query_object.end_time:
            start_date = datetime.strptime(query_object.begin_time, '%Y-%m-%d')
            end_date = datetime.strptime(query_object.end_time, '%Y-%m-%d')
            end_time = datetime.combine(end_date, time(23, 59, 59))
            query = query.where(and_(Customer.create_time >= start_date, Customer.create_time <= end_time))

        # 排序
        query = query.order_by(desc(Customer.create_time))

        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def get_customer_page_list(cls, db: AsyncSession, query_object: CustomerPageQueryModel):
        """
        获取客户分页列表

        :param db: orm对象
        :param query_object: 分页查询参数对象
        :return: 客户分页列表
        """
        query = select(Customer).where(Customer.del_flag == '0')
        
        customer_id_processed = False
        # 优先处理 customer_id，根据其级别（集团或公司）调整查询逻辑
        if hasattr(query_object, 'customer_id') and query_object.customer_id:
            main_customer = await cls.get_customer_by_id(db, query_object.customer_id)
            if main_customer:
                if main_customer.customer_level == '1':  # 集团客户
                    query = query.where(
                        or_(
                            Customer.customer_id == query_object.customer_id,
                            Customer.parent_id == query_object.customer_id
                        )
                    )
                    customer_id_processed = True
                elif main_customer.customer_level == '2':  # 公司客户
                    query = query.where(Customer.customer_id == query_object.customer_id)
                    customer_id_processed = True
            # else: customer_id 无效或未找到客户，customer_id_processed 保持 False

        # 根据一般查询条件过滤
        if query_object.customer_name:
            query = query.where(Customer.customer_name.like(f'%{query_object.customer_name}%'))
        
        # 如果 customer_id 未被特殊处理，则应用 customer_level 过滤器
        if not customer_id_processed and query_object.customer_level:
            query = query.where(Customer.customer_level == query_object.customer_level)
            
        if query_object.customer_type:
            query = query.where(Customer.customer_type == query_object.customer_type)
        if query_object.customer_source:
            query = query.where(Customer.customer_source == query_object.customer_source)
        if query_object.province:
            query = query.where(Customer.province == query_object.province)
        if query_object.city:
            query = query.where(Customer.city == query_object.city)
        if query_object.status:
            query = query.where(Customer.status == query_object.status)
            
        # 如果 customer_id 未被特殊处理，并且 query_object 中仍有 customer_id (例如，特殊处理中客户未找到)
        # 则应用通用的 customer_id 过滤器
        if not customer_id_processed and hasattr(query_object, 'customer_id') and query_object.customer_id:
            query = query.where(Customer.customer_id == query_object.customer_id)

        # 如果 customer_id 未被特殊处理，则应用 parent_id 过滤器
        if not customer_id_processed and hasattr(query_object, 'parent_id') and query_object.parent_id:
            query = query.where(Customer.parent_id == query_object.parent_id)

        # 时间范围查询
        if query_object.begin_time and query_object.end_time:
            start_date = datetime.strptime(query_object.begin_time, '%Y-%m-%d')
            end_date = datetime.strptime(query_object.end_time, '%Y-%m-%d')
            end_time = datetime.combine(end_date, time(23, 59, 59))
            query = query.where(and_(Customer.create_time >= start_date, Customer.create_time <= end_time))

        # 排序
        query = query.order_by(desc(Customer.create_time))

        # 分页
        return await PageUtil.paginate(db=db, query=query, page_num=query_object.page_num, page_size=query_object.page_size, is_page=True)

    @classmethod
    async def get_customer_page_by_manager_ids(cls, db: AsyncSession, query_object: CustomerPageQueryModel, manager_user_ids: List[int]):
        """
        根据负责人ID列表获取客户分页列表
        
        :param db: orm对象
        :param query_object: 分页查询参数对象
        :param manager_user_ids: 负责人用户ID列表
        :return: 客户分页列表
        """
        # 先查询这些负责人负责的客户ID
        manager_query = select(CustomerInternalManager.customer_id).where(
            CustomerInternalManager.user_id.in_(manager_user_ids)
        ).distinct()
        
        manager_result = await db.execute(manager_query)
        customer_ids = [row[0] for row in manager_result.fetchall()]
        
        if not customer_ids:
            # 如果没有找到客户，返回空结果
            from utils.page_util import PageResponseModel
            return PageResponseModel(
                rows=[],
                total=0,
                page_num=query_object.page_num,
                page_size=query_object.page_size
            )
        
        # 基于客户ID列表查询客户信息
        query = select(Customer).where(
            Customer.del_flag == '0',
            Customer.customer_id.in_(customer_ids)
        )
        
        customer_id_processed = False
        # 优先处理 customer_id，根据其级别（集团或公司）调整查询逻辑
        if hasattr(query_object, 'customer_id') and query_object.customer_id:
            main_customer = await cls.get_customer_by_id(db, query_object.customer_id)
            if main_customer and main_customer.customer_id in customer_ids:
                if main_customer.customer_level == '1':  # 集团客户
                    # 获取集团下的所有公司ID
                    child_query = select(Customer.customer_id).where(
                        Customer.parent_id == query_object.customer_id,
                        Customer.del_flag == '0'
                    )
                    child_result = await db.execute(child_query)
                    child_customer_ids = [row[0] for row in child_result.fetchall()]
                    
                    # 过滤出在负责人客户列表中的客户
                    valid_customer_ids = [query_object.customer_id] + [cid for cid in child_customer_ids if cid in customer_ids]
                    query = query.where(Customer.customer_id.in_(valid_customer_ids))
                    customer_id_processed = True
                elif main_customer.customer_level == '2':  # 公司客户
                    query = query.where(Customer.customer_id == query_object.customer_id)
                    customer_id_processed = True
        
        # 根据一般查询条件过滤
        if query_object.customer_name:
            query = query.where(Customer.customer_name.like(f'%{query_object.customer_name}%'))
        
        # 如果 customer_id 未被特殊处理，则应用 customer_level 过滤器
        if not customer_id_processed and query_object.customer_level:
            query = query.where(Customer.customer_level == query_object.customer_level)
            
        if query_object.customer_type:
            query = query.where(Customer.customer_type == query_object.customer_type)
        if query_object.customer_source:
            query = query.where(Customer.customer_source == query_object.customer_source)
        if query_object.province:
            query = query.where(Customer.province == query_object.province)
        if query_object.city:
            query = query.where(Customer.city == query_object.city)
        if query_object.status:
            query = query.where(Customer.status == query_object.status)
        if hasattr(query_object, 'parent_id') and query_object.parent_id:
            query = query.where(Customer.parent_id == query_object.parent_id)
        
        # 时间范围查询
        if query_object.begin_time and query_object.end_time:
            start_date = datetime.strptime(query_object.begin_time, '%Y-%m-%d')
            end_date = datetime.strptime(query_object.end_time, '%Y-%m-%d')
            end_time = datetime.combine(end_date, time(23, 59, 59))
            query = query.where(and_(Customer.create_time >= start_date, Customer.create_time <= end_time))
        
        # 排序
        query = query.order_by(desc(Customer.create_time))
        
        return PageUtil.get_page_result(query, db, query_object)

    @classmethod
    async def get_customer_by_id(cls, db: AsyncSession, customer_id: int):
        """
        根据客户ID获取客户详情

        :param db: orm对象
        :param customer_id: 客户ID
        :return: 客户详情
        """
        query = select(Customer).where(Customer.customer_id == customer_id, Customer.del_flag == '0')
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def get_customer_by_name(cls, db: AsyncSession, customer_name: str):
        """
        根据客户名称获取客户详情

        :param db: orm对象
        :param customer_name: 客户名称
        :return: 客户详情
        """
        query = select(Customer).where(Customer.customer_name == customer_name, Customer.del_flag == '0')
        result = await db.execute(query)
        return result.scalars().first()

    @classmethod
    async def add_customer(cls, db: AsyncSession, customer: Customer):
        """
        新增客户

        :param db: orm对象
        :param customer: 客户对象
        :return: 新增的客户对象
        """
        db.add(customer)
        await db.flush()
        return customer

    @classmethod
    async def update_customer(cls, db: AsyncSession, customer: Customer):
        """
        更新客户

        :param db: orm对象
        :param customer: 客户对象
        :return: 更新结果
        """
        query = update(Customer).where(Customer.customer_id == customer.customer_id).values(
            customer_name=customer.customer_name,
            customer_level=customer.customer_level,
            parent_id=customer.parent_id,
            customer_type=customer.customer_type,
            customer_source=customer.customer_source,
            province=customer.province,
            city=customer.city,
            district=customer.district,
            address=customer.address,
            status=customer.status,
            update_by=customer.update_by,
            update_time=customer.update_time,
            remark=customer.remark,
            customer_importance=customer.customer_importance
        )
        result = await db.execute(query)
        return result.rowcount

    @classmethod
    async def delete_customer(cls, db: AsyncSession, customer_id: int, update_by: str):
        """
        删除客户（逻辑删除）

        :param db: orm对象
        :param customer_id: 客户ID
        :param update_by: 更新人
        :return: 删除结果
        """
        query = update(Customer).where(Customer.customer_id == customer_id).values(
            del_flag='2',
            update_by=update_by,
            update_time=datetime.now()
        )
        result = await db.execute(query)
        return result.rowcount

    @classmethod
    async def get_customer_contacts(cls, db: AsyncSession, customer_id: int):
        """
        获取客户联系人列表

        :param db: orm对象
        :param customer_id: 客户ID
        :return: 联系人列表
        """
        query = select(CustomerContact).where(
            CustomerContact.customer_id == customer_id,
            CustomerContact.status == '0'
        ).order_by(desc(CustomerContact.is_primary), desc(CustomerContact.create_time))
        result = await db.execute(query)
        return result.scalars().all()

    @classmethod
    async def add_customer_contact(cls, db: AsyncSession, contact: CustomerContact):
        """
        新增客户联系人

        :param db: orm对象
        :param contact: 联系人对象
        :return: 新增的联系人对象
        """
        db.add(contact)
        await db.flush()
        return contact

    @classmethod
    async def update_customer_contact(cls, db: AsyncSession, contact: CustomerContact):
        """
        更新客户联系人

        :param db: orm对象
        :param contact: 联系人对象
        :return: 更新结果
        """
        query = update(CustomerContact).where(CustomerContact.contact_id == contact.contact_id).values(
            contact_name=contact.contact_name,
            position=contact.position,
            phone=contact.phone,
            wechat=contact.wechat,
            email=contact.email,
            is_primary=contact.is_primary,
            status=contact.status,
            update_by=contact.update_by,
            update_time=contact.update_time,
            remark=contact.remark
        )
        result = await db.execute(query)
        return result.rowcount

    @classmethod
    async def delete_customer_contact(cls, db: AsyncSession, contact_id: int, update_by: str):
        """
        删除客户联系人

        :param db: orm对象
        :param contact_id: 联系人ID
        :param update_by: 更新人
        :return: 删除结果
        """
        query = update(CustomerContact).where(CustomerContact.contact_id == contact_id).values(
            status='1',
            update_by=update_by,
            update_time=datetime.now()
        )
        result = await db.execute(query)
        return result.rowcount

    @classmethod
    async def get_customer_internal_managers(cls, db: AsyncSession, customer_id: int):
        """
        获取客户内部负责人列表

        :param db: orm对象
        :param customer_id: 客户ID
        :return: 内部负责人列表
        """
        query = select(CustomerInternalManager, SysUser).where(
            CustomerInternalManager.customer_id == customer_id,
            CustomerInternalManager.user_id == SysUser.user_id
        ).order_by(desc(CustomerInternalManager.is_primary), desc(CustomerInternalManager.create_time))
        result = await db.execute(query)
        return result.all()

    @classmethod
    async def add_customer_internal_manager(cls, db: AsyncSession, manager: CustomerInternalManager):
        """
        新增客户内部负责人

        :param db: orm对象
        :param manager: 内部负责人对象
        :return: 新增的内部负责人对象
        """
        db.add(manager)
        await db.flush()
        return manager

    @classmethod
    async def update_customer_internal_manager(cls, db: AsyncSession, manager: CustomerInternalManager):
        """
        更新客户内部负责人

        :param db: orm对象
        :param manager: 内部负责人对象
        :return: 更新结果
        """
        query = update(CustomerInternalManager).where(CustomerInternalManager.id == manager.id).values(
            user_id=manager.user_id,
            is_primary=manager.is_primary,
            update_by=manager.update_by,
            update_time=manager.update_time
        )
        result = await db.execute(query)
        return result.rowcount

    @classmethod
    async def delete_customer_internal_manager(cls, db: AsyncSession, manager_id: int):
        """
        删除客户内部负责人

        :param db: orm对象
        :param manager_id: 内部负责人关联ID
        :return: 删除结果
        """
        query = delete(CustomerInternalManager).where(CustomerInternalManager.id == manager_id)
        result = await db.execute(query)
        return result.rowcount
